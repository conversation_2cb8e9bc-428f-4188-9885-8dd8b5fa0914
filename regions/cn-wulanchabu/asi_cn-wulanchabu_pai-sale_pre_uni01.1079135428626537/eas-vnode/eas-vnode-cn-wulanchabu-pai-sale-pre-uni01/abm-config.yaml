aliyunCsClusterId: c879761fede8a4ead818ea060e7777dd3
aliyunCsEndpoint: asi-vpc.cn-wulanchabu.aliyuncs.com
aliyunDefaultEnterpriseRegistryIp: **********
aliyunEciEndpoint: eci.aliyuncs.com
aliyunEnableEci: true
aliyunPaiAcrPvlServiceId: epsrv-0jlzrs9qof7bfy16rqix
aliyunPaiAcrUrl: dsw-registry-vpc
aliyunResourceAccessId: LTAI5tDqiodkPVXWZzJ1h92J
aliyunResourceAccessKey: ******************************
aliyunResourceAccountEciRole: AliyunPAIDSWToECIRole
aliyunResourceAccountUid: "****************"
aliyunServiceAccessId: LTAI4Fd28HXiC3ifbQNMxf2Z
aliyunServiceAccessKey: ******************************
aliyunServiceAccountEciRole: aliyunpaidswdefaultrole
aliyunServiceAccountUid: "****************"
applyCrd: true
cluster_service_account_uid: "****************"
clusterControllerImage: cluster-controller-amd64:0.0.1-d9e88d1e3c
commonClusterType: asi_public_cloud
commonRegion: cn-wulanchabu
aliyunAckEndpoint: cs-vpc.cn-wulanchabu.aliyuncs.com
dockerRegistryNamespace: pai-common
dockerRegistryVpcPrefix: dsw-registry-vpc
featuresVirtualClusterUpdateGrayList: all
gatewayControllerImage: gateway-controller-amd64:0.0.1-17170f89c4
imagesContourImage: contour:v1.30.3
imagesEasQueueControllerImage: eas-queue-controller:pnode_20250521113202_eabe949
imagesEnvoyImage: envoy:v1.31.6
imagesGpuExporterImage: gpu-exporter:v3.1.8-1.8.2-2.6.6-46212f0-aliyun
imagesKubeStateMetricsImage: kube-state-metrics:2.13.0
imagesLogtailImage: logtail:v1.4.0.0-aliyun
imagesMetricsExporterAgentImage: metrics-exporter-agent-amd64:0.0.1-17170f89c
imagesMetricsExporterImage: metrics-exporter-amd64:0.0.1-17170f89c
imagesMetricsExporterPrometheusImage: prometheus:********
imagesNodeExporterImage: node-exporter:********
imagesPersistenceAgentImage: persistence-agent:release-20250513105907-b45d1289
imagesRouterImage: bashbase:0.0.1-069e6174b
imagesVirtualNodeControllerImage: vnode-controller-amd64:0.0.1-d9e88d1e3c
imagesWebhookImage: eas-webhook-amd64:0.0.1-0fddb5efd
kubernetesServerHost: **********
kubernetesServerPort: "443"
paiResourceServiceDbAddress: pai_resource_service_stagging:Pai_resource_service_stagging@tcp(*********:3306)/pai_resource_service_cn_wulanchabu_stagging?charset=utf8&parseTime=True
paiResourceServiceDbEndpoint: *********
replicas: "2"
resourceType: ""
