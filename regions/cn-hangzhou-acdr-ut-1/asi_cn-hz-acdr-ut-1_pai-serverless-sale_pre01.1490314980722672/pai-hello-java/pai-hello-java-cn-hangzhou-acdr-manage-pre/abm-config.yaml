deployConfig:
  image:
    tag: $image1Tag
    repository: pai-zhijiang-acdr-prod-registry-vpc.cn-hangzhou-acdr-ut-1.cr.aliyuncs.com/pai-common/pai-hello-java
    pullPolicy: Always
  replicaCount: 1
  resources:
    requests:
      cpu: "2"
      memory: "4Gi"
    limits:
      cpu: "4"
      memory: "8Gi"
  service:
    port: 80
    type: ClusterIP
  env:
    - name: JAVA_MEMORY_OPTS
      value: "-Xms4500m -Xmx4500m -Xmn1500m"
    - name: JAVA_CUSTOM_OPTS
      value: ""
    - name: POD_CPU_REQUEST
      valueFrom:
        resourceFieldRef:
          resource: requests.cpu
          divisor: 1m
    - name: POD_CPU_LIMIT
      valueFrom:
        resourceFieldRef:
          resource: limits.cpu
          divisor: 1m
  podLabels:
    sigma.ali/app-name: "pai-hello-java"
    sigma.ali/instance-group: "pai-hello-java-cn-hangzhou-acdr-manage-pre"
    sigma.ali/site: ""
    sigma.alibaba-inc.com/app-unit: "CENTER_UNIT.center"
    sigma.alibaba-inc.com/app-stage: "PRE_PUBLISH"
    sigma.ali/inject-staragent-sidecar: "true"
    "secret-csi.alibabacloud.com/enable": "true"
  podAnnotations:
    "secret-csi.alibabacloud.com/inject-containers": "*"
    "pod.beta1.alibabacloud.com/sshd-in-staragent": "true"
  tolerations:
    - effect: NoSchedule
      key: sigma.ali/resource-pool
      operator: Equal
      value: pai-manage

applicationYaml:
  spring:
    application.name: "pai-hello-java"
    #默认生效的环境配置
    profiles.active: "local"

  akless:
    enable: false
    regionId: "cn-hangzhou"
    accessKeyId: "LTAItj7vI4awSPH7"
    accessKeySecret: "zXJ1E37GK5zeeUEshEJdA71gmak6lY"
    arn: "acs:ram::1223350723184953:role/pai-hello-java-cloud-product"
    arnTesting: "acs:ram::1223350723184953:role/pai-hello-java-cloud-product-testing"
    bootstrapToken: "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
