tensorboard:
  deployment:
    # args: '"-isPreEnv=true", "-enableAuth=false", "-env=public", "-region=cn-hangzhou", "-bucUrl=", "-appCode=", "-accessKeyId=LTAI5tDcKVKGdsrebyxskiNy","-accessKeySecret=******************************", "-aliyunPk=", "-oauthCallbackHost=pre-pai-dlc-proxy-cn-hangzhou.aliyun.com", "-paiDlcServicedomain=pai-dlc-pre.cn-hangzhou.aliyuncs.com", "-certificateFilePath=/etc/kubelet-client/encryptedData", "-serviceAccountAKId=LTAI5tDqiodkPVXWZzJ1h92J", "-serviceAccountAKSecret=******************************", "-isEncrypt=false", "-decryptAKId=", "-decryptAKSecret="'
    args: '"-isPreEnv=true", "-enableAuth=false", "-env=public", "-region=cn-hangzhou", "-bucUrl=", "-appCode=", "-accessKeyId=","-accessKeySecret=", "-aliyunPk=", "-oauthCallbackHost=pre-pai-dlc-proxy-cn-hangzhou.aliyun.com", "-paiDlcServicedomain=pai-dlc-pre.cn-hangzhou.aliyuncs.com", "-certificateFilePath=/etc/kubelet-client/encryptedData", "-serviceAccountAKId=", "-serviceAccountAKSecret=", "-isEncrypt=false", "-decryptAKId=", "-decryptAKSecret=", "-chinaAASEndpoint=aas.aliyuncs.com", "-intlAASEndpoint=aas.ap-southeast-1.aliyuncs.com","-chinaAccountLoginUrlFormat=https://account.aliyun.com/login/login.htm?type=aliyunLoginAdapter&oauth_callback=http://","-intlAccountLoginUrlFormat=https://account.alibabacloud.com/login/login.htm?type=aliyunLoginAdapter&oauth_callback=http://","-dlcProxyAliyuncsHostRegPatternInPreEnv=^(pre-pai|pre-pai4service)-dlc-proxy-\\d*-?%s(-vpc)?\\.aliyuncs\\.com$","-dlcProxyAliyuncsHostRegPattern=^(pai|pai4service)-dlc-proxy-\\d*-?%s(-vpc)?\\.aliyuncs\\.com$","-enableCredentialProvider=true","-appName=pai-public-dsw-app","-resourceAccountRoleArn=acs:ram::****************:role/pai-public-dsw-app-resource-account-pai-notebook","-whiteListAccountRoleArn=acs:ram::****************:role/pai-public-dsw-app-resource-account-pai-web"'
    image: dsw-registry-vpc.cn-hangzhou.cr.aliyuncs.com/pai-common/pai-dlc-proxy:idpt-2-********-b1bd1d62c
    hostPort: 2023
    labels:
      sigma.ali/app-name: "pai-public-dsw-app"
      sigma.ali/instance-group: "pai-public-dsw-app-cn-hangzhou-pre"
      sigma.ali/site: ""
      sigma.alibaba-inc.com/app-unit: "CENTER_UNIT.center"
      sigma.alibaba-inc.com/app-stage: "PRE_PUBLISH"
      secret-csi.alibabacloud.com/enable: "true"
  ingress:
    enabled: true
    loadBalancerId: "alb-lla901drh6ft9i9a3p"
    port: 80
    protocol: "HTTP"
    hosts:
      host: pai-dlc-proxy-cn-hangzhou.aliyun.com
  isLingjun: "false"
  jobTolerations:
    - effect: NoSchedule
      key: sigma.ali/resource-pool
      operator: Equal
      value: pai
  preinstalljob:
    args: '"-isEncrypt=false", "-encryptAKId=", "-encryptAKSecret=", "-region=", "-csrName=webterminal-csr", "-userName=pai-dlc-proxy-webterminal", "-secretName=terminal-secret", "-namespace=dlc", "-keyName=encryptedData"'
    image: dsw-registry.cn-beijing.cr.aliyuncs.com/pai-common/kms-encrypted-secret:1.3
  service:
    vswitchId: vsw-bp13iflw4518zp878d4tm
  tenantresource:
    image: m-bp1auzqc5m7fvln1urb7
    instanceCount: "4"
  tolerations:
    - effect: NoSchedule
      key: sigma.ali/resource-pool
      operator: Equal
      value: pai
  environment: public
  podAnnotations:
    pods.sigma.alibaba-inc.com/inject-pod-sn: "true"
    secret-csi.alibabacloud.com/inject-containers: "*"
tensorboardNodeAffinityExpressions:
  - key: sigma.ali/resource-pool
    operator: In
    values:
      - pai
