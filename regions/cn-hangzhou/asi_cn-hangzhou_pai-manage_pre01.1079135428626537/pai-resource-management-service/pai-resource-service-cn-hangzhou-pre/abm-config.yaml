deployConfig:
  image:
    tag: feature-20250612_dsw_support_assign_nodes-3706a158
    repository: >-
      dsw-registry-vpc.cn-hangzhou.cr.aliyuncs.com/pai-common/pai-resource-service
    pullPolicy: Always
  replicaCount: 2
  resources:
    limits:
      cpu: "4"
      memory: "16Gi"
    requests:
      cpu: "4"
      memory: "16Gi"
  kubeClusterId: ceeb37b6a91f9425395928d060f9b6968
  service:
    vswitchId: vsw-bp1ser1ixpqjblnu1ldrq
    loadBalancerId: lb-bp1qm1exl984539sw9s58
    netLoadBalancerId: nlb-905rfxy1xvlyoxv483
    port: 80
    type: LoadBalancer
  hostAliases:
    - ip: "***************"
      hostnames:
        - "aiworkspace-share.cn-hangzhou.aliyuncs.com"
        - "aiworkspaceadmin-pre.cn-hangzhou.aliyuncs.com"
    - ip: "***************"
      hostnames:
        - "commondriver-inner.vpc-proxy.aliyuncs.com"
    - ip: "**************"
      hostnames:
        - "commondriver-intl-inner.vpc-proxy.aliyuncs.com"
    - ip: "***************"
      hostnames:
        - "pai-dlc-inner-pre.cn-hangzhou.aliyuncs.com"
        - "tag-share.cn-hangzhou.aliyuncs.com"
  skyline:
    appName: pai-resource-management-service
    site: 
    appUnit: CENTER_UNIT.center
    appGroup: pai-resource-management-service-hangzhou-pre
    appStage: PRE_PUBLISH
clusterConfigs:
  c9bc8179a6a674235a85a74f997d50211:
    apiVersion: v1
    clusters:
      - cluster:
          insecure-skip-tls-verify: true
          server: >-
            https://ep-bp1ia16819672eea650b.epsrv-bp16nc7yn90jgma1ai8l.cn-hangzhou.privatelink.aliyuncs.com:6443
        name: c9bc8179a6a674235a85a74f997d50211
    contexts:
      - context:
          cluster: c9bc8179a6a674235a85a74f997d50211
          user: pai-dlc-service
        name: default
    current-context: default
    kind: Config
    preferences: {}
    users:
      - name: pai-dlc-service
        user:
          client-certificate-data: >-
            LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURJakNDQWdxZ0F3SUJBZ0lJVEJ0cmFEdThXZHd3RFFZSktvWklodmNOQVFFTEJRQXdQakVuTUE4R0ExVUUKQ2hNSWFHRnVaM3BvYjNVd0ZBWURWUVFLRXcxaGJHbGlZV0poSUdOc2IzVmtNUk13RVFZRFZRUURFd3ByZFdKbApjbTVsZEdWek1CNFhEVEkxTURJd056QTRNall5TlZvWERUUTFNREl3TXpBNE1EUXlNMW93R2pFWU1CWUdBMVVFCkF4TVBjR0ZwTFdSc1l5MXpaWEoyYVdObE1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0MKQVFFQXhicXlFRzJwdEhDR0pDN1E3ZU1wbm5IcWI2all1S0J5bVVBcUw1aWl6YkJ5NDdnRG5EaVJyY2xVb0Y5cwo4YUU5R1hWTFNtTlpuVHVkTm9VNTBmMkYyQXc1VUdBQ2xjUjFyejlobkdMV2lkSytBTHQrM1RSRkVRaE1PRG93CnR6SDFJc1dqRVVsVEpsUWtYbkxLMDFWTkx0R01zb09jNnFtZTR4d3FIQU1UMWt3SnlIUVcrWEZZUXgycTdqSEoKZWN4WklFQ1owU0hsb01IajZ5OHl0UStsTlVGdGU0cGMrRXJPbzZwWjRmcEhSMEkzVnp3ckkwcGZPVU12Mzd3UQpXcDRnc1RGSWFrV3NRcUlwZE80dTB3UWt3SlFlN3JIalB6RU9qclhSNXpBMXZ3alptNlN6YmlXYXpIR1VyNlRWCm91ckRLNDZnR0NPaTEwRUQ5VFUzeGU3azF3SURBUUFCbzBnd1JqQU9CZ05WSFE4QkFmOEVCQU1DQmFBd0V3WUQKVlIwbEJBd3dDZ1lJS3dZQkJRVUhBd0l3SHdZRFZSMGpCQmd3Rm9BVVRpTitpOWRFdTJzeWJPR0JjaFlyY2NoNgp0M2t3RFFZSktvWklodmNOQVFFTEJRQURnZ0VCQUljTzYrN1gzU0NDdjljaEQrMXdGUDZzbzYvQk8rcWh1YlJmCnppcFBSWlVzTGtrVWI3TXdEL3c1ek1GYWZqUmNrRU94c3RXVHpGWjkrcUUzUTRrRStyaUZlQmRLODFDMlk3QjUKa2dDN05HUHJQbC9PeTZhM3Bnbm12K1Vqc0lORVUvbXFMNlQ2OTRpMjBRcFIrSkhTaXVzT0g2VFY1MkI1M3BYSAp4NHpCbEVtTzQ3bWQ4WTB3ZEV2VS95bTRQSW5BZGhRc3FmcmQyTXpSTTYxL28xWVV3MElERU1NRWxkVGlkUGRFCkJMQWFSQWRoWE1IM05DdVBvVGxQSDJ1R2pLamI5OWJTZ2tsenVxTm9TOTZWVUxwaDh2bFlydGM3bUh3NGtQaUcKTFRKdDgxQkR6UXIzNCtLZ0pzWmJqK0ZQQUJDM0dKODNydk5taklwb3VEcU0wS1pjdVpnPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          client-key-data: >-
            ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  ceeb37b6a91f9425395928d060f9b6968:
    preferences: {}
    apiVersion: v1
    kind: Config
    current-context: default
    contexts:
      - context:
          cluster: ceeb37b6a91f9425395928d060f9b6968
          user: pai-dlc-service
        name: default
    clusters:
      - cluster:
          server: 'https://*************:6443'
          insecure-skip-tls-verify: true
        name: ceeb37b6a91f9425395928d060f9b6968
    users:
      - name: pai-dlc-service
        user:
          client-certificate-data: >-
            LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURBVENDQWVtZ0F3SUJBZ0lJWThwQU90dUROQ2d3RFFZSktvWklodmNOQVFFTEJRQXdQakVuTUJRR0ExVUUKQ2hNTllXeHBZbUZpWVNCamJHOTFaREFQQmdOVkJBb1RDR2hoYm1kNmFHOTFNUk13RVFZRFZRUURFd3ByZFdKbApjbTVsZEdWek1CNFhEVEl4TVRFeU1qQTJNamN3TUZvWERUUTBNRE13TnpBek1qQTBOMW93R2pFWU1CWUdBMVVFCkF4TVBjR0ZwTFdSc1l5MXpaWEoyYVdObE1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBTUlJQkNnS0MKQVFFQXlRQlNzOVNwYUJFd0htUzRFUG0wenpyZXE5S05TZ2FwSU5kSGpGOHVRVnlWVStOSDd4MDVJeWUrd1NuOAo1bEhENTlMd0xTZGJUNGdxZDUweHI0d1NjUFYvVjBKbjZWdjB2amxwTHN2bVBDODRkSjdiWUQ2MEFYUldmb3dTClp5clhKQmJadnk4MzIzRzI4N0JXVnI3Vjd6dXJ3bW5GRmpZRTRldFp3b2ZtKytQVkM0bGdtNW5rS24wUEQzSkcKZTVBSDcyV1dIa05MZzluNVo3WlcxQ0ZUclMvNFFBM1pFRmZ2eGkxRmc5NEVySWg5Sk1EUFZYd0NQRDlwbGdGOQowTFNGa0xRM24rcVUvNzN1blhUWjczTFBZeDNnZ2t0VTdlampscWloTnV1NjZ5TkVVS21PeTZzUFNiVElNQ3ZyCjZaUVFKU0hCcEg5em0rMWhyc1NwZzhDMmNRSURBUUFCb3ljd0pUQU9CZ05WSFE4QkFmOEVCQU1DQmFBd0V3WUQKVlIwbEJBd3dDZ1lJS3dZQkJRVUhBd0l3RFFZSktvWklodmNOQVFFTEJRQURnZ0VCQUNUSldydTlIYlZQZXhmZQpQUlNmaGxzaHNsRHJnSmcrZXJUYmsyd21nMEtyMlRGM3RNSy9YNHpqdm5SUm9yS3Q1RjdHMTJhQjhxOEw4OUd1CjhzZDAxUHhuMzZicEV4Tzl1YVpIV28yVDFyTnlrOFZVWkN1RnkvcHNjUW8vYnQ5aEZTOU95TTdJTmRKbmtWNm4KUllMQTVaU0dad0FlTERQbXh2aCtpV3BXUFVhMnRMTlVtSjlCNk13WDVyeWl5QllEZEcxZlZtMmpRVVVsTjVFMgozc2JaOHpOMjdLMldzdU5LYm8wNDFIS1hrSFZ4WnFhYVV0RUlOOFhQNGVTWXA0a2Fzc2h4YklyT2FZbTlWL0tECjRIYkJpZU5zMkYxRWtwYjEvZjJ4WnpoZzlRS3VCYVk4Rm0rTVdKQkpoQ0I1R1BBRld6TTZ3ZU14Nmp2eXZWVEoKdkl1c0Y1VT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          client-key-data: >-
            ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
metricsConfigs:
  c9bc8179a6a674235a85a74f997d50211:
    project: pai-metrics-cn-hangzhou-dpu-1-pre01
    logstore: asi-cn-hangzhou-dpu-pre01-metrics
  ceeb37b6a91f9425395928d060f9b6968:
    project: pai-metrics-cn-hangzhou-pre1
    logstore: asi-cn-hangzhou-dlc-j01-metrics
bizConfig:
  cloud_monitor_proxy:
    enabled: true
    access_key_id: TFRBSTV0Tlk4WkJUNWk4NmZCVDlIa2E2
    access_key_secret: ****************************************
    endpoint: metrics-vpc.cn-hangzhou.aliyuncs.com
    quota_namespace: acs_pai_quota
  service_port: 8087
  ecs_image_id: m-bp1auzqc5m7fvln1urb7
  ecs_image_id_of_gn8t: aliyun_3_x64_20G_uefi_alibase_20230727.vhd
  pprof:
    enable: true
    port: 7777
  tenant_ids_to_integrate_with_aiworkspace_auth:
  enable_running_instance_check_refund: false
  enable_order_by_node: true
  enable_cordon_expired_nodes: true
  enable_external_cluster:
  maxium_node_of_resource_group: 500
  maximum_concurrent_creating_machinegroups: 1000
  threshold_hours_of_expiring_machine_group: 120
  ntm:
    ak_id: TFRBSTV0RHFpb2RrUFZYV1p6SjFoOTJK
    ak_secret: ****************************************
    endpoint: commondriver-vpc-inner-pre.cn-hangzhou.aliyuncs.com
    intl_endpoint: commondriver-intl-inner.vpc-proxy.aliyuncs.com
    timeout_minutes: 15
  resource_limit:
    enabled: true
    account_service_endpoint: accountlabel-vpc-inner.cn-hangzhou.aliyuncs.com
    account_service_ak: TFRBSTRGZDI4SFhpQzNpZmJRTk14ZjJa
    account_service_ak_secret: ****************************************
    account_service_token: 6FC6391C7544748CF2982CE40B6EEA153400889E
    account_service_label_series: gc_level
    default_rule_dlc: CPU:33,GPU:2
    default_rule_dsw: CPU:8,GPU:2
    default_rule_general: CPU:8,GPU:2
    gclevel_threshold: GC6
  region_id: cn-hangzhou
  app_name: pai-resource-management-service
  environment: public
  env_type: staging1
  cluster_type: asi
  auth_user_id_of_innerapi: ****************
  enable_machine_model_in_stock: true
  prom_query_timeout_seconds: 20
  resources:
    gpu_types_str: A100-80G:A100-SXM4-80GB:nvidia.com/gpu-a100-sxm4-80gb,Tesla-T4:Tesla-T4:nvidia.com/gpu-tesla-t4,GeForce-3090:GeForce-RTX-3090:nvidia.com/gpu-geforce-rtx-3090,A10:A10:nvidia.com/gpu-a10,GeForce-2080-Ti:GeForce-RTX-2080-Ti:nvidia.com/gpu-geforce-rtx-2080-ti,Tesla-V100-16G:Tesla-V100-SXM2-16GB:nvidia.com/gpu-tesla-v100-sxm2-16gb,Tesla-V100-32G:Tesla-V100-SXM2-32GB:nvidia.com/gpu-tesla-v100-sxm2-32gb
  # aliyun common info
  aliyunAK:
    access_key_id: 4PxM0Y+OSy2wfvaaGKGmy2M2oYdM5hLAW2gAM5meQ78=
    access_key_secret: d+jPx1P3cP71AUgqT7SqfrOFLr/PyLP9IUaA0APuh48=
  # what storage type we want to use: apiserver, mysql
  backend_storage_type: mysql
  # whether or not to enable white list for resource service
  enable_white_list: true
  # whether or not to enable multiple cluster for resource service
  enable_multiple_clusters: true
  # cluster id of lingjun cluster in that region
  cluster_id_of_lingjun: ceeb37b6a91f9425395928d060f9b6968:Default,c9bc8179a6a674235a85a74f997d50211:DPU
  public_cluster:
    enable_ecs: true
    enable_lingjun: false
  # what storage type we want to use for log: apiserver, sls, elasticsearch
  log_storage_type: sls
  metric_storage_type: sls_metricstore
  # prometheus backend environment info
  metric_backend_prometheus:
    # endpoint of prometheus
    endpoint_url: http://cn-hangzhou-intranet.arms.aliyuncs.com:9090/api/v1/prometheus/******************************/****************/ceeb37b6a91f9425395928d060f9b6968/cn-hangzhou
    enable_gpu_exporter: false
    network_devices: eth0
  # prometheus config
  prometheus:
    enable_metrics_export: true
  ram_auth_service:
    enable: true
    endpoint: http://ram-internal-vpc.cn-hangzhou.aliyun-inc.com
    client_cache_size: 10000
    client_cache_expired_time_in_minutes: 15
    service_code_list_str: pai
  dsw_service:
    ak_id: TFRBSTV0RGNLVktHZHNyZWJ5eHNraU55
    ak_secret: ****************************************
    endpoint: pai-dsw-vpc-pre.cn-hangzhou.aliyuncs.com
    enable: true
  dlc_service:
    ak_id: TFRBSTV0RGNLVktHZHNyZWJ5eHNraU55
    ak_secret: ****************************************
    ops_ak_id: LTAI5tDkVhwzjWms7cG6HVDk
    ops_ak_secret: ******************************
    endpoint: pai-dlc-vpc-pre.cn-hangzhou.aliyuncs.com
    inner_endpoint: pai-dlc-vpc-inner-pre.cn-hangzhou.aliyuncs.com
    enable: true
  # service account ak & ak_secret to call AI Workspace open api
  ai_workspace:
    ak_id: TFRBSTV0RGNLVktHZHNyZWJ5eHNraU55
    ak_secret: ****************************************
    enable: true
    endpoint: aiworkspace-vpc-pre.cn-hangzhou.aliyuncs.com
    members_cache_ttl_mins: 1
    ops_akid: TFRBSTV0RGNLVktHZHNyZWJ5eHNraU55
    ops_aksecret: ****************************************
    inner_endpoint: aiworkspaceadmin-pre.cn-hangzhou.aliyuncs.com
  # what storage type we want to use for user: ldap, mysql, linux, ram or buc
  user_storage_type: mock
  k8s_client:
    enable_cache: true
  backend_database:
    host: ep-bp1ie4c85d9a86439dd0.epsrv-bp1vpx9k1xn8yir25rao.cn-hangzhou.privatelink.aliyuncs.com
    port: 3306
    database_name: pai_resource_service_cn_hangzhou_stagging
    user: pai_resource_service_stagging
    password: Pai_resource_service_stagging
    group_concat_max_len:
    enable_logging:
  # define the root path of kube config
  kube_config_root_path: ./kube_config
  # sls backend environment info
  metric_backend_sls:
    endpoint: http://cn-hangzhou-intranet.log.aliyuncs.com
    project_ecs: pai-metrics-cn-hangzhou-pre1
    logstore_ecs: asi-cn-hangzhou-dlc-j01-metrics
    project_lingjun:
    logstore_lingjun:
    access_key_id: LTAIFGTnNJF2bgjg
    access_key_secret: ******************************
  # sls backend environment info
  log_backend_sls:
    endpoint: cn-hangzhou.log.aliyuncs.com
    project: pai-dlc-public-staging-hangzhou
    logstore_stdout: k8s-stdout
    logstore_event: k8s-event
  # logger config
  logger:
    # the path of the log file when output log in different levels
    paths:
      info: logs/info.log
      error: logs/error.log
    # the maximum size in megabytes of the log file before it gets rotated
    max_size: 100
    # the maximum number of old log files to retain
    max_backups: 100
    # the maximum number of days to retain old log files
    max_age: 365
    # determines if the rotated log files should be compressed
    compress: false
    # determines if you are local debugging
    debug_mode: false
  git_sync:
    enable_set_image:
    image:
  # service account AK
  service_account:
    accesss_key_id: TFRBSTRHR1BjSFpUNHBvZUNuQTZRV2Fz
    accesss_key_secret: ****************************************
    sts_endpoint: sts-vpc-inner.cn-hangzhou.aliyuncs.com
  # resource account AK
  resource_account:
    accesss_key_id: TFRBSVZkSjVtYVFjYlZudQ==
    accesss_key_secret: ****************************************
  user:
    # the storage location where user id saves in: http-header or session
    user_id_storage_location:
    http_header_key: x-acs-caller-uid
    http_header_key_of_tenant_id: x-acs-parent-id
    trusted_service_account_list_str: ****************,****************,****************,****************
    proxy_service_account_list_str: ****************
  # log config
  log_config:
    retrieve_max_lines: 2000
    retrieve_max_days_before: 7
    download_max_lines: 25000
    download_max_routines_count: 20
    enable_get_pod_logs_optimized: true
    optimized_log_search_cutoff_time: "2025-06-01T00:00:00"
  # resource manager
  resource_manager:
    leader_election_storage_type: mysql
    enable_leader_election: true
    handle_tenant_concurrency: 8
    handle_quota_concurrency: 8
    handle_queue_concurrency:
    enable_resource_group_reconcile: true
  # quota
  quota:
    enable: true
    default_root_quota: production-root
    enable_limited_role_access: true
    limited_role_access_account_list_str: ****************
    enable_position_info: true
    enable_sync_workload_info: true
    enable_quota_tasks: true
    max_quota_count_per_tenant: 10000
    max_active_task_count_per_quota: 50
    enable_user_resource_limit: true
    disable_aiworkspace_data: false
    enable_lingjun_for_all_tenant: true
    enable_sub_quota_config: true
    obtain_usage_from_db: true
  # spot
  spot:
    enable: false
    closed_with_stock_threshold:
  # ACS configuration
  acs:
    enable: true
    endpoint: acc-pre.cn-hangzhou.aliyuncs.com
  # EAS info
  eas:
    access_key_id: LTAI4GDC8F1SjpYMm8EQibFv
    access_key_secret: ******************************
  vpc:
    endpoint: vpc-vpc.cn-hangzhou.aliyuncs.com
  ecs:
    enable: true
    endpoint: ecs.aliyuncs.com
    spot_price_cache_ttl_minutes: 15
    describe_spot_price_concurrency: 16
  # acdr ntm
  enable_order_fill_cluster_id: false
  enable_aliyun_client_v4_signature: false
  enable_tag_resource: true
  # kms
  kms:
    enable: false
    endpoint: TODO
    access_key_id: TODO
    access_key_secret: TODO
  signature:
    enable: false
    secret: TODO
  message:
    enable:
    msc_endpoint_cn:
    msc_endpoint_intl:
    region_show_name_zh_base64:
    region_show_name_zh:
    region_show_name_en:
    latency_for_checking_heal_action_block:
    latency_for_node_cordon:
  # cache
  cache:
    ecs_spec_sync_interval_in_seconds:
  node:
    obtain_node_from_db: true
    enable_resource_synchronization: true
  enable_original_gpu_type: false
  cache_service:
    enable: true
  tag_inner:
    endpoint: tag-vpc-inner.cn-hangzhou.aliyuncs.com
  service_account_pai: acs:ram::****************:role/zeus-pai-resource-service-service-account-pai
  service_account_eas_pai: acs:ram::****************:role/zeus-pai-resource-service-service-account-eas-pai
  resource_account_pai_web: acs:ram::****************:role/pai-resource-service-resource-account-pai-web
  resource_account_pai_notebook: acs:ram::****************:role/pai-resource-service-resource-account-pai-notebook
  resource_account_pai_ops: acs:ram::****************:role/pai-resource-service-resource-account-pai-ops
  inner_pop_pai_notebook: acs:ram::****************:role/pai-resource-service-inner-pop-pai-notebook
  bootstrap_token: ************************************************************************************************.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ew0kkkWMcT2cPkeoyLce9irf7WNsc0Zq5c9okxdhH8V1uhtWLKk-5SJw9LZ4w-L3FfDL6ro58kQkIHxY0z_8tsOADqbUiOFdIXFIpeSOJxqGQGHOc3eWQHk0umsz5BzYNVY1ECrYTotzhYAb5TaghHSCXYJUXrQ6PEidYQjAIPkgSIgMArlpon3HYfO71_PCJ0ThLY5-urO6rtVXDmv6MCKfI1qFP4HLZf16f7QR7SU-9byQKLrF_Oi3K9wDAeg5Zwx9oNMDTp_7-nT7oYxRhfFrqOAMdyxFCnnY4lVc-u_dbkLaN9MRlsUz0tu95fJ-6MyTDyJ0sUMvaihTtIwANg
armsConfig:
  enabled: true
  appName: pai-resource-management-service-pre
  licenseKey: amiryzr42x@ed51bec78f0964a
