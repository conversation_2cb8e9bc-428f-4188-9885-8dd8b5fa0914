# Capacity Lock Reconciler 测试用例

## 测试概述

本文档包含了 Capacity Lock Reconciler 配置控制功能的完整测试用例，用于验证系统的单点控制、条件初始化和 API 保护功能。

## 测试环境配置

### 配置文件位置
- 配置文件：`pkg/pai-resource-service/config/service_config/config.yml`
- 关键配置项：
```yaml
capacity_lock_reconciler:
  enabled: true/false
```

## 测试用例表格

| 测试分类     | 测试用例ID | 测试场景                       | 配置状态         | 测试步骤                                                                                                                                                    | 预期结果                                                                                      | 优先级 | 状态     |
| ------------ | ---------- | ------------------------------ | ---------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- | ------ | -------- |
| **配置控制** | TC001      | Reconciler 禁用状态启动        | `enabled: false` | 1. 设置配置为 false<br>2. 启动服务<br>3. 检查启动日志                                                                                                       | 日志显示："Capacity lock reconcile controller is disabled"<br>无 cron job 启动日志            | 🔴 高   | ✅ 通过   |
| **API保护**  | TC002      | 创建 Capacity Lock (禁用)      | `enabled: false` | POST /capacitylocks<br>```json<br>{"tenantId":"test","instanceType":"ecs.g6.large","zoneId":"cn-hangzhou-h","paymentType":"postpaid","requestedCount":1}``` | HTTP 错误响应<br>错误信息："Capacity lock reconciler is disabled..."                          | 🔴 高   | ✅ 通过   |
| **API保护**  | TC003      | 更新 Capacity Lock (禁用)      | `enabled: false` | PATCH /capacitylocks/123<br>```json<br>{"requestedCount":2}```                                                                                              | HTTP 错误响应<br>错误信息："Capacity lock reconciler is disabled..."                          | 🔴 高   | ✅ 通过   |
| **API保护**  | TC004      | 删除 Capacity Lock (禁用)      | `enabled: false` | DELETE /capacitylocks/123                                                                                                                                   | HTTP 错误响应<br>错误信息："Capacity lock reconciler is disabled..."                          | 🔴 高   | ✅ 通过   |
| **API保护**  | TC005      | 查询 Capacity Lock 列表 (禁用) | `enabled: false` | GET /capacitylocks                                                                                                                                          | HTTP 错误响应<br>错误信息："Capacity lock reconciler is disabled..."                          | 🔴 高   | ✅ 通过   |
| **API保护**  | TC005a     | 查询单个 Capacity Lock (禁用)  | `enabled: false` | GET /capacitylocks/123                                                                                                                                      | HTTP 错误响应<br>错误信息："Capacity lock reconciler is disabled..."                          | 🔴 高   | ✅ 通过   |
| **动态配置** | TC006      | 配置热切换 (启用→禁用)         | `true` → `false` | 1. 以 true 启动<br>2. 修改配置为 false<br>3. 重启服务<br>4. 测试 API                                                                                        | 重启后 reconciler 停止<br>API 调用被拒绝                                                      | 🟡 中   | ✅ 通过   |
| **并发测试** | TC007      | 并发 API 请求 (禁用)           | `enabled: false` | 同时发送 10 个 POST 请求                                                                                                                                    | 所有请求都返回相同错误信息<br>无异常或崩溃                                                    | 🟡 中   | ✅ 通过   |
| **日志测试** | TC008      | API 拒绝日志                   | `enabled: false` | 调用被保护的 API 并检查日志                                                                                                                                 | 有相应的拒绝访问日志记录                                                                      | 🟢 低   | ✅ 通过   |
| **回归测试** | TC009      | 其他功能不受影响               | `enabled: false` | 测试非 capacity lock 相关的 API                                                                                                                             | 所有其他功能正常工作<br>数据库连接正常                                                        | 🟡 中   | ✅ 通过   |
| **数据兼容** | TC010      | 现有数据查询 (禁用状态)        | `enabled: false` | 查询已存在的 capacity lock 数据                                                                                                                             | HTTP 错误响应<br>错误信息："Capacity lock reconciler is disabled..."<br>整个功能模块不可用    | 🔴 高   | ✅ 通过   |
| **配置控制** | TC011      | Reconciler 启用状态启动        | `enabled: true`  | 1. 设置配置为 true<br>2. 启动服务<br>3. 检查启动日志                                                                                                        | 日志显示："Capacity lock reconcile controller started successfully"<br>显示 cron job 添加日志 | 🔴 高   | ⏳ 待测试 |
| **API功能**  | TC012      | 创建 Capacity Lock (启用)      | `enabled: true`  | POST /capacitylocks<br>```json<br>{"tenantId":"test","instanceType":"ecs.g6.large","zoneId":"cn-hangzhou-h","paymentType":"postpaid","requestedCount":1}``` | 成功创建<br>返回 capacity lock ID<br>状态为 CREATING                                          | 🔴 高   | ⏳ 待测试 |
| **API功能**  | TC013      | 更新 Capacity Lock (启用)      | `enabled: true`  | PATCH /capacitylocks/123<br>```json<br>{"requestedCount":2}```                                                                                              | 成功更新<br>状态变为 RECONCILING                                                              | 🔴 高   | ⏳ 待测试 |
| **API功能**  | TC014      | 删除 Capacity Lock (启用)      | `enabled: true`  | DELETE /capacitylocks/123                                                                                                                                   | 成功标记删除<br>状态变为 RELEASING                                                            | 🔴 高   | ⏳ 待测试 |
| **动态配置** | TC015      | 配置热切换 (禁用→启用)         | `false` → `true` | 1. 以 false 启动<br>2. 修改配置为 true<br>3. 重启服务<br>4. 测试 API                                                                                        | 重启后 reconciler 正常工作<br>API 调用成功                                                    | 🟡 中   | ⏳ 待测试 |
| **数据兼容** | TC016      | 启用后处理现有数据             | `false` → `true` | 1. 禁用状态下有数据<br>2. 启用 reconciler<br>3. 检查数据处理                                                                                                | 现有数据能被 reconciler 正常处理                                                              | 🟡 中   | ⏳ 待测试 |
| **配置控制** | TC017      | 配置项缺失测试                 | 配置文件中无此项 | 1. 删除配置项<br>2. 启动服务<br>3. 检查行为                                                                                                                 | 默认为 false，系统正常启动但 reconciler 禁用                                                  | 🟡 中   | ⏳ 待测试 |
| **性能测试** | TC018      | 资源使用对比                   | 对比测试         | 1. 分别以 true/false 启动<br>2. 监控内存和 CPU<br>3. 对比资源使用                                                                                           | 禁用时资源使用明显更少<br>无后台任务运行                                                      | 🟢 低   | ⏳ 待测试 |
| **日志测试** | TC019      | 启动日志完整性                 | 两种配置         | 检查启动日志的完整性和清晰度                                                                                                                                | 日志清晰显示 reconciler 状态<br>无错误或警告日志                                              | 🟡 中   | ⏳ 待测试 |
| **边界测试** | TC020      | 无效配置值测试                 | 各种无效值       | 测试配置为 null、空字符串等                                                                                                                                 | 系统能正确处理并默认为 false                                                                  | 🟢 低   | ⏳ 待测试 |

## 测试优先级说明

- 🔴 **高优先级**：核心功能，必须通过
- 🟡 **中优先级**：重要功能，建议测试
- 🟢 **低优先级**：边界情况，时间允许时测试

## 测试状态说明

- ⏳ **待测试**：尚未开始测试
- 🧪 **测试中**：正在进行测试
- ✅ **通过**：测试通过
- ❌ **失败**：测试失败，需要修复
- 🔄 **重测**：需要重新测试

## 关键测试数据

### API 测试数据
```json
// 创建请求示例
{
  "tenantId": "test-tenant-001",
  "instanceType": "ecs.g6.large",
  "zoneId": "cn-hangzhou-h",
  "paymentType": "postpaid",
  "requestedCount": 1,
  "description": "测试用容量锁"
}

// 更新请求示例
{
  "requestedCount": 2,
  "description": "更新后的描述"
}
```

### 预期错误信息
```
Capacity lock reconciler is disabled. Please enable it in configuration to use this feature.
```

## 测试执行建议

1. **按优先级执行**：先执行高优先级测试用例
2. **分环境测试**：在测试环境和预发环境都要验证
3. **记录详细结果**：每个测试用例都要记录详细的执行结果
4. **性能基线**：建立性能测试的基线数据
5. **自动化考虑**：核心测试用例考虑自动化实现

## 测试完成标准

- [ ] 所有高优先级测试用例通过
- [ ] 至少 80% 的中优先级测试用例通过
- [ ] 无严重性能回归
- [ ] 日志输出清晰准确
- [ ] 错误信息用户友好
